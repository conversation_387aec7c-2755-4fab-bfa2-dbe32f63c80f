.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%, #0a0a0a 100%);
  padding: 20px;
  overflow: hidden;
}

/* AI神经网络背景 */
.login-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    /* 神经网络节点 */
    radial-gradient(circle at 15% 20%, rgba(255, 255, 255, 0.15) 3px, transparent 3px),
    radial-gradient(circle at 85% 30%, rgba(255, 255, 255, 0.12) 2px, transparent 2px),
    radial-gradient(circle at 25% 70%, rgba(255, 255, 255, 0.18) 4px, transparent 4px),
    radial-gradient(circle at 75% 80%, rgba(255, 255, 255, 0.14) 3px, transparent 3px),
    radial-gradient(circle at 45% 15%, rgba(255, 255, 255, 0.16) 2px, transparent 2px),
    radial-gradient(circle at 65% 60%, rgba(255, 255, 255, 0.13) 3px, transparent 3px),
    /* 微妙的电路板纹理 */
    linear-gradient(45deg, transparent 48%, rgba(255, 255, 255, 0.03) 49%, rgba(255, 255, 255, 0.03) 51%, transparent 52%),
    linear-gradient(-45deg, transparent 48%, rgba(255, 255, 255, 0.02) 49%, rgba(255, 255, 255, 0.02) 51%, transparent 52%);
  background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 80px 80px, 60px 60px;
  z-index: 0;
  animation: neuralPulse 25s infinite ease-in-out;
}

/* 学习进步轨迹线条 */
.login-container::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    /* 上升曲线路径 */
    linear-gradient(135deg, transparent 0%, transparent 48%, rgba(255, 255, 255, 0.08) 49%, rgba(255, 255, 255, 0.08) 51%, transparent 52%, transparent 100%),
    linear-gradient(45deg, transparent 0%, transparent 48%, rgba(255, 255, 255, 0.06) 49%, rgba(255, 255, 255, 0.06) 51%, transparent 52%, transparent 100%),
    /* 知识连接网格 */
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 200px 200px, 150px 150px, 60px 60px, 60px 60px;
  background-position: 0 0, 50px 50px, 0 0, 0 0;
  z-index: 0;
  animation: progressFlow 30s infinite linear;
}

@keyframes neuralPulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  33% {
    opacity: 0.9;
    transform: scale(1.02);
  }
  66% {
    opacity: 0.8;
    transform: scale(0.98);
  }
}

@keyframes progressFlow {
  0% {
    background-position: 0 0, 50px 50px, 0 0, 0 0;
  }
  100% {
    background-position: 200px 200px, 250px 250px, 60px 60px, 60px 60px;
  }
}

/* AI数据流光点效果容器 */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.particle {
  position: absolute;
  pointer-events: none;
}

/* AI数据节点 - 大型智能核心 */
.particle:nth-child(1) {
  width: 16px;
  height: 16px;
  top: 15%;
  left: 25%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.1) 70%, transparent 100%);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3), inset 0 0 10px rgba(255, 255, 255, 0.2);
  animation: aiPulse 20s infinite ease-in-out;
}

/* 学习进度光点 - 上升轨迹 */
.particle:nth-child(2) {
  width: 12px;
  height: 12px;
  top: 70%;
  left: 15%;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
  animation: learningProgress 25s infinite ease-in-out;
}

/* 知识连接点 - 网络节点 */
.particle:nth-child(3) {
  width: 8px;
  height: 8px;
  top: 35%;
  left: 75%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 0 12px rgba(255, 255, 255, 0.3);
  animation: knowledgeFlow 18s infinite ease-in-out;
  animation-delay: -3s;
}

/* 练习成果光点 - 成长轨迹 */
.particle:nth-child(4) {
  width: 14px;
  height: 14px;
  top: 55%;
  left: 80%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.35) 0%, rgba(255, 255, 255, 0.08) 100%);
  box-shadow: 0 0 18px rgba(255, 255, 255, 0.25);
  animation: practiceGrowth 22s infinite ease-in-out;
  animation-delay: -6s;
}

/* 智能思维火花 */
.particle:nth-child(5) {
  width: 6px;
  height: 6px;
  top: 25%;
  left: 60%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  animation: thinkingSpark 15s infinite ease-in-out;
  animation-delay: -2s;
}

/* 教育启发光点 */
.particle:nth-child(6) {
  width: 20px;
  height: 20px;
  top: 45%;
  left: 35%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 60%, transparent 100%);
  box-shadow: 0 0 25px rgba(255, 255, 255, 0.2);
  animation: educationInspire 28s infinite ease-in-out;
  animation-delay: -10s;
}

/* AI智能脉冲动画 */
@keyframes aiPulse {
  0%, 100% {
    transform: translateY(0) scale(1);
    opacity: 0.6;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3), inset 0 0 10px rgba(255, 255, 255, 0.2);
  }
  25% {
    transform: translateY(-30px) scale(1.1);
    opacity: 0.9;
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.5), inset 0 0 15px rgba(255, 255, 255, 0.3);
  }
  50% {
    transform: translateY(-15px) scale(0.95);
    opacity: 0.7;
    box-shadow: 0 0 25px rgba(255, 255, 255, 0.4), inset 0 0 12px rgba(255, 255, 255, 0.25);
  }
  75% {
    transform: translateY(20px) scale(1.05);
    opacity: 0.8;
    box-shadow: 0 0 35px rgba(255, 255, 255, 0.4), inset 0 0 18px rgba(255, 255, 255, 0.3);
  }
}

/* 学习进步轨迹动画 */
@keyframes learningProgress {
  0% {
    transform: translateY(0) translateX(0) scale(1);
    opacity: 0.5;
  }
  25% {
    transform: translateY(-80px) translateX(40px) scale(1.2);
    opacity: 0.9;
  }
  50% {
    transform: translateY(-120px) translateX(80px) scale(1.1);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-60px) translateX(120px) scale(1.3);
    opacity: 1;
  }
  100% {
    transform: translateY(-20px) translateX(160px) scale(0.8);
    opacity: 0.6;
  }
}

/* 知识流动动画 */
@keyframes knowledgeFlow {
  0%, 100% {
    transform: translateY(0) translateX(0) rotate(0deg);
    opacity: 0.6;
  }
  33% {
    transform: translateY(-50px) translateX(-30px) rotate(120deg);
    opacity: 0.9;
  }
  66% {
    transform: translateY(30px) translateX(-60px) rotate(240deg);
    opacity: 0.7;
  }
}

/* 练习成长动画 */
@keyframes practiceGrowth {
  0% {
    transform: translateY(0) scale(0.8);
    opacity: 0.4;
  }
  20% {
    transform: translateY(-40px) scale(1.1);
    opacity: 0.8;
  }
  40% {
    transform: translateY(-60px) scale(1.3);
    opacity: 1;
  }
  60% {
    transform: translateY(-80px) scale(1.2);
    opacity: 0.9;
  }
  80% {
    transform: translateY(-100px) scale(1.4);
    opacity: 0.7;
  }
  100% {
    transform: translateY(-120px) scale(1.1);
    opacity: 0.5;
  }
}

/* 思维火花动画 */
@keyframes thinkingSpark {
  0%, 100% {
    transform: translateY(0) translateX(0) scale(1);
    opacity: 0.8;
  }
  25% {
    transform: translateY(-25px) translateX(15px) scale(1.5);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) translateX(-20px) scale(0.8);
    opacity: 0.6;
  }
  75% {
    transform: translateY(15px) translateX(25px) scale(1.2);
    opacity: 0.9;
  }
}

/* 教育启发动画 */
@keyframes educationInspire {
  0%, 100% {
    transform: translateY(0) translateX(0) scale(1);
    opacity: 0.3;
  }
  20% {
    transform: translateY(-40px) translateX(20px) scale(1.1);
    opacity: 0.6;
  }
  40% {
    transform: translateY(-20px) translateX(-30px) scale(0.9);
    opacity: 0.8;
  }
  60% {
    transform: translateY(30px) translateX(40px) scale(1.2);
    opacity: 0.7;
  }
  80% {
    transform: translateY(10px) translateX(-20px) scale(1.05);
    opacity: 0.5;
  }
}

/* 神经网络连接线 */
.neural-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.connection-svg {
  width: 100%;
  height: 100%;
  opacity: 0.4;
}

.connection-path {
  stroke-dasharray: 5, 10;
  animation: connectionFlow 15s infinite linear;
}

@keyframes connectionFlow {
  0% {
    stroke-dashoffset: 0;
    opacity: 0.2;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    stroke-dashoffset: 100;
    opacity: 0.2;
  }
}

/* 学习进步指示器 */
.progress-indicators {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.progress-dot {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.4);
  animation: progressDotPulse 3s infinite ease-in-out;
}

.progress-dot:nth-child(1) { animation-delay: 0s; }
.progress-dot:nth-child(2) { animation-delay: 0.5s; }
.progress-dot:nth-child(3) { animation-delay: 1s; }
.progress-dot:nth-child(4) { animation-delay: 1.5s; }

.progress-arrow {
  position: absolute;
  top: 12%;
  left: 65%;
  font-size: 24px;
  color: rgba(255, 255, 255, 0.3);
  animation: arrowPulse 4s infinite ease-in-out;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

@keyframes progressDotPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.5);
    opacity: 1;
  }
}

@keyframes arrowPulse {
  0%, 100% {
    opacity: 0.3;
    transform: translateY(0) scale(1);
  }
  50% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.1);
  }
}

/* 教育主题装饰元素 */
.login-container .education-symbols {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

/* 知识灯泡符号 */
.login-container .education-symbols::before {
  content: "💡";
  position: absolute;
  top: 25%;
  right: 15%;
  font-size: 20px;
  opacity: 0.15;
  animation: symbolFloat 20s infinite ease-in-out;
  filter: grayscale(100%) brightness(2);
}

/* 书本学习符号 */
.login-container .education-symbols::after {
  content: "📚";
  position: absolute;
  bottom: 30%;
  left: 12%;
  font-size: 18px;
  opacity: 0.12;
  animation: symbolFloat 25s infinite ease-in-out;
  animation-delay: -8s;
  filter: grayscale(100%) brightness(2);
}

@keyframes symbolFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.12;
  }
  25% {
    transform: translateY(-20px) rotate(5deg);
    opacity: 0.18;
  }
  50% {
    transform: translateY(-10px) rotate(-3deg);
    opacity: 0.15;
  }
  75% {
    transform: translateY(15px) rotate(2deg);
    opacity: 0.20;
  }
}

/* AI芯片纹理效果 */
.login-container .ai-circuit {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
  background-image:
    /* 微电路线条 */
    linear-gradient(0deg, transparent 48%, rgba(255, 255, 255, 0.02) 49%, rgba(255, 255, 255, 0.02) 51%, transparent 52%),
    linear-gradient(90deg, transparent 48%, rgba(255, 255, 255, 0.02) 49%, rgba(255, 255, 255, 0.02) 51%, transparent 52%),
    /* 芯片节点 */
    radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.05) 2px, transparent 2px),
    radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.05) 2px, transparent 2px),
    radial-gradient(circle at 60% 20%, rgba(255, 255, 255, 0.05) 2px, transparent 2px),
    radial-gradient(circle at 30% 80%, rgba(255, 255, 255, 0.05) 2px, transparent 2px);
  background-size: 120px 120px, 120px 120px, 100% 100%, 100% 100%, 100% 100%, 100% 100%;
  animation: circuitPulse 30s infinite ease-in-out;
}

@keyframes circuitPulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}

/* 数据流动轨迹 */
.login-container .data-streams {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
  overflow: hidden;
}

.data-stream {
  position: absolute;
  width: 2px;
  height: 40px;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.4), transparent);
  border-radius: 1px;
}

.data-stream:nth-child(1) {
  left: 20%;
  animation: dataFlow1 12s infinite linear;
}

.data-stream:nth-child(2) {
  left: 45%;
  animation: dataFlow2 15s infinite linear;
  animation-delay: -3s;
}

.data-stream:nth-child(3) {
  left: 70%;
  animation: dataFlow3 18s infinite linear;
  animation-delay: -6s;
}

.data-stream:nth-child(4) {
  left: 85%;
  animation: dataFlow1 20s infinite linear;
  animation-delay: -9s;
}

@keyframes dataFlow1 {
  0% {
    top: -50px;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    top: 100vh;
    opacity: 0;
  }
}

@keyframes dataFlow2 {
  0% {
    top: -50px;
    opacity: 0;
    transform: translateX(0);
  }
  10% {
    opacity: 1;
  }
  50% {
    transform: translateX(20px);
  }
  90% {
    opacity: 1;
  }
  100% {
    top: 100vh;
    opacity: 0;
    transform: translateX(-10px);
  }
}

@keyframes dataFlow3 {
  0% {
    top: -50px;
    opacity: 0;
    transform: translateX(0) scaleY(1);
  }
  10% {
    opacity: 1;
  }
  30% {
    transform: translateX(-15px) scaleY(1.5);
  }
  70% {
    transform: translateX(10px) scaleY(0.8);
  }
  90% {
    opacity: 1;
  }
  100% {
    top: 100vh;
    opacity: 0;
    transform: translateX(0) scaleY(1);
  }
}

/* 知识积累效果 */
.login-container .knowledge-accumulation {
  position: absolute;
  bottom: 10%;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  z-index: 0;
}

.knowledge-accumulation::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  animation: knowledgeGrow 8s infinite ease-in-out;
}

@keyframes knowledgeGrow {
  0% {
    left: -100%;
  }
  50% {
    left: 0%;
  }
  100% {
    left: 100%;
  }
}

.login-background {
  width: 100%;
  max-width: 400px;
  position: relative;
  z-index: 1;
}

.login-content {
  position: relative;
  z-index: 1;
}

.login-card {
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  backdrop-filter: blur(15px) saturate(180%);
  background: rgba(255, 255, 255, 0.97);
  border: 1px solid rgba(255, 255, 255, 0.15);
  padding: 24px;
  position: relative;
  z-index: 10;
  transition: all 0.3s ease;
}

.login-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 20px;
  z-index: -1;
}

.login-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-logo-container {
  margin-bottom: 16px;
}

.login-logo {
  height: 64px;
  width: auto;
}

.login-title {
  color: #333 !important;
  margin-bottom: 8px !important;
  font-weight: 600;
  font-size: 28px;
}

.login-subtitle {
  font-size: 14px;
  color: #666;
}

.login-form {
  margin-bottom: 24px;
}

.login-form .ant-form-item {
  margin-bottom: 20px;
}

.login-form .ant-input-affix-wrapper,
.login-form .ant-input-password {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
  background: rgba(255, 255, 255, 0.9);
}

.login-form .ant-input-affix-wrapper:hover,
.login-form .ant-input-password:hover {
  border-color: #666 !important;
}

.login-form .ant-input-affix-wrapper:focus,
.login-form .ant-input-affix-wrapper-focused,
.login-form .ant-input-password:focus,
.login-form .ant-input-password-focused {
  border-color: #333 !important;
  box-shadow: 0 0 0 2px rgba(51, 51, 51, 0.2) !important;
}

.login-button {
  height: 44px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #333 0%, #000 100%) !important;
  border: none !important;
  transition: all 0.3s;
  color: white !important;
}

.login-button:hover,
.login-button:focus {
  background: linear-gradient(135deg, #555 0%, #333 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  border-color: transparent !important;
  color: white !important;
}

.login-button:active {
  transform: translateY(0);
  background: linear-gradient(135deg, #222 0%, #000 100%) !important;
}

.login-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }
  
  .login-card {
    padding: 16px;
  }
  
  .login-title {
    font-size: 24px;
  }
}

/* 加载状态动画 */
.login-button.ant-btn-loading {
  background: linear-gradient(135deg, #333 0%, #000 100%);
}

/* 表单验证错误样式 */
.login-form .ant-form-item-has-error .ant-input-affix-wrapper,
.login-form .ant-form-item-has-error .ant-input-password {
  border-color: #ff4d4f;
}

.login-form .ant-form-item-has-error .ant-input-affix-wrapper:focus,
.login-form .ant-form-item-has-error .ant-input-password:focus {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* 图标样式 */
.login-form .anticon {
  color: #666 !important;
}

.login-form .ant-input-affix-wrapper:focus .anticon,
.login-form .ant-input-password:focus .anticon {
  color: #333 !important;
}

/* 版权信息样式 */
.login-footer .ant-typography {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* 确保所有Ant Design组件都遵循黑白主题 */
.login-form .ant-form-item-explain-error {
  color: #ff4d4f !important;
}

.login-form .ant-form-item-label > label {
  color: #333 !important;
}

/* 覆盖任何可能的蓝色主题色 */
.ant-btn-primary {
  background: linear-gradient(135deg, #333 0%, #000 100%) !important;
  border-color: transparent !important;
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background: linear-gradient(135deg, #555 0%, #333 100%) !important;
  border-color: transparent !important;
}

.ant-input:focus,
.ant-input-focused {
  border-color: #333 !important;
  box-shadow: 0 0 0 2px rgba(51, 51, 51, 0.2) !important;
}
