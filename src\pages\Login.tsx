import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, message, Space } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { login } from '../services/auth';
import { useAuthStore } from '../utils/authStore';
import './Login.css';

const { Title, Text } = Typography;

interface LoginFormValues {
  username: string;
  password: string;
}

const Login: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { login: setAuth } = useAuthStore();

  const handleSubmit = async (values: LoginFormValues) => {
    setLoading(true);
    try {
      const user = await login(values.username, values.password);
      setAuth(user);
      message.success('登录成功！');
      navigate('/dashboard');
    } catch (error: any) {
      console.error('Login error:', error);

      // 处理不同类型的错误
      let errorMessage = '登录失败，请稍后重试';

      if (error.response) {
        // 服务器响应错误
        const status = error.response.status;
        const data = error.response.data;

        console.log('Error response:', { status, data });

        if (status === 400) {
          errorMessage = '用户名或密码错误';
        } else if (status === 401) {
          errorMessage = '用户名或密码错误';
        } else if (status === 422) {
          errorMessage = '请求参数格式错误';
        } else if (status === 500) {
          errorMessage = '服务器内部错误，请稍后重试';
        } else if (data && data.detail) {
          errorMessage = typeof data.detail === 'string' ? data.detail : '登录失败';
        } else if (data && data.message) {
          errorMessage = typeof data.message === 'string' ? data.message : '登录失败';
        }
      } else if (error.request) {
        // 网络错误
        errorMessage = '网络连接失败，请检查网络设置';
      } else if (error.message) {
        // 其他错误
        errorMessage = error.message;
      }

      // 确保错误消息显示
      console.log('Showing error message:', errorMessage);
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      {/* AI数据流光点效果 */}
      <div className="particles">
        <div className="particle" title="AI智能核心"></div>
        <div className="particle" title="学习进度轨迹"></div>
        <div className="particle" title="知识连接网络"></div>
        <div className="particle" title="练习成长轨迹"></div>
        <div className="particle" title="智能思维火花"></div>
        <div className="particle" title="教育启发光点"></div>
      </div>

      {/* 神经网络连接线 */}
      <div className="neural-connections">
        <svg className="connection-svg" viewBox="0 0 100 100" preserveAspectRatio="none">
          <defs>
            <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="rgba(255,255,255,0.1)" />
              <stop offset="50%" stopColor="rgba(255,255,255,0.3)" />
              <stop offset="100%" stopColor="rgba(255,255,255,0.1)" />
            </linearGradient>
          </defs>
          <path d="M15,20 Q40,10 65,30 T85,60" stroke="url(#connectionGradient)" strokeWidth="0.5" fill="none" className="connection-path" />
          <path d="M25,70 Q50,50 75,80" stroke="url(#connectionGradient)" strokeWidth="0.3" fill="none" className="connection-path" />
          <path d="M10,50 Q35,30 60,60 T90,40" stroke="url(#connectionGradient)" strokeWidth="0.4" fill="none" className="connection-path" />
        </svg>
      </div>

      {/* 学习进步指示器 */}
      <div className="progress-indicators">
        <div className="progress-dot" style={{top: '20%', left: '10%'}}></div>
        <div className="progress-dot" style={{top: '15%', left: '25%'}}></div>
        <div className="progress-dot" style={{top: '10%', left: '40%'}}></div>
        <div className="progress-dot" style={{top: '8%', left: '55%'}}></div>
        <div className="progress-arrow">↗</div>
      </div>

      {/* 教育主题装饰符号 */}
      <div className="education-symbols"></div>

      {/* AI芯片纹理背景 */}
      <div className="ai-circuit"></div>

      {/* 数据流动效果 */}
      <div className="data-streams">
        <div className="data-stream"></div>
        <div className="data-stream"></div>
        <div className="data-stream"></div>
        <div className="data-stream"></div>
      </div>

      {/* 知识积累进度条 */}
      <div className="knowledge-accumulation"></div>

      <div className="login-background">
        <div className="login-content">
          <Card className="login-card" variant="borderless">
            <div className="login-header">
              <div className="login-logo-container">
                <img src="/logo.png" alt="锤磨AI" className="login-logo" />
              </div>
              <Title level={2} className="login-title">
                锤磨AI
              </Title>
              <Text type="secondary" className="login-subtitle">
                欢迎登录AI课后陪练系统
              </Text>
            </div>

            <Form
              form={form}
              name="login"
              onFinish={handleSubmit}
              autoComplete="off"
              size="large"
              className="login-form"
            >
              <Form.Item
                name="username"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 2, message: '用户名至少2个字符' },
                  { max: 50, message: '用户名不能超过50个字符' }
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="请输入用户名"
                  autoComplete="username"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码至少6个字符' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="请输入密码"
                  autoComplete="current-password"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                  icon={<LoginOutlined />}
                  className="login-button"
                >
                  {loading ? '登录中...' : '登录'}
                </Button>
              </Form.Item>
            </Form>

            <div className="login-footer">
              <Space direction="vertical" size="small" style={{ width: '100%', textAlign: 'center' }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  © 2025 Trainingmore.com All rights reserved.
                </Text>
              </Space>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Login;
